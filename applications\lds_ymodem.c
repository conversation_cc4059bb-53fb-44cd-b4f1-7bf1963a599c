/**
 * @file lds_ymodem.c
 * @brief YModem协议升级模块实现
 * <AUTHOR>
 * @date 2024
 */

/* 相关头文件 */
#include "lds_ymodem.h"
/* C库 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
/* 其他库的.h */
#include <n32g45x.h>
/* 本项目内的.h */
#include "main.h"
#include "lds_log.h"
#include "lds_flash.h"

/* 全局变量定义 */
static uint8_t g_ymodemBuffer[YMODEM_PACKET_SIZE_1024 + YMODEM_PACKET_HEADER + YMODEM_PACKET_CRC];
static uint32_t g_currentAddr = 0;
static uint32_t g_receivedSize = 0;
static uint8_t g_expectedPacketNum = 0;

/**
 * @brief 串口发送单个字节
 * @param byte 要发送的字节
 */
static void ldsYmodemSendByte(uint8_t byte)
{
    /* 等待发送缓冲区空 */
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXDE) == RESET) {
        /* 等待发送缓冲区空闲 */
    }
    USART_SendData(USART1, byte);
}

/**
 * @brief 串口接收单个字节（带超时）
 * @param byte 接收字节的指针
 * @param timeoutMs 超时时间（毫秒）
 * @return true 接收成功, false 超时
 */
static bool ldsYmodemReceiveByte(uint8_t* byte, uint32_t timeoutMs)
{
    uint32_t count = 0;

    if (byte == NULL) {
        return false;
    }

    while (count < timeoutMs * 1000) { /* 简单计数延时 */
        if (USART_GetFlagStatus(USART1, USART_FLAG_RXDNE) != RESET) {
            *byte = USART_ReceiveData(USART1);
            return true;
        }
        count++;
    }
    return false;
}

/**
 * @brief 计算CRC16校验值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验值
 */
static uint16_t ldsYmodemCalcCrc16(const uint8_t* data, uint16_t length)
{
    uint16_t crc = 0;

    if (data == NULL) {
        return 0;
    }

    while (length--) {
        crc ^= (uint16_t)(*data++) << 8;
        for (uint8_t i = 0; i < 8; i++) {
            if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}

/**
 * @brief 接收YModem数据包
 * @param packet 数据包缓冲区
 * @param packetSize 数据包大小指针
 * @param expectedPacketNum 期望的包序号
 * @return lds_ymodem_result_t 接收结果
 */
static lds_ymodem_result_t ldsYmodemReceivePacket(uint8_t* packet, uint16_t* packetSize, uint8_t expectedPacketNum)
{
    uint8_t header;
    uint8_t packetNum, packetNumInv;
    uint16_t crcReceived, crcCalculated;
    uint16_t dataSize;

    if (packet == NULL || packetSize == NULL) {
        return YMODEM_ERROR;
    }

    /* 接收包头 */
    if (!ldsYmodemReceiveByte(&header, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }

    if (header == YMODEM_EOT) {
        *packetSize = 0;
        return YMODEM_OK;
    }

    if (header == YMODEM_CAN) {
        return YMODEM_CANCEL;
    }

    if (header != YMODEM_SOH && header != YMODEM_STX) {
        return YMODEM_ERROR;
    }

    dataSize = (header == YMODEM_SOH) ? YMODEM_PACKET_SIZE_128 : YMODEM_PACKET_SIZE_1024;

    /* 接收包序号和反码 */
    if (!ldsYmodemReceiveByte(&packetNum, YMODEM_TIMEOUT_MS) ||
        !ldsYmodemReceiveByte(&packetNumInv, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }

    if ((packetNum + packetNumInv) != 0xFF) {
        return YMODEM_ERROR;
    }

    /* 验证包序号（除了第一个包） */
    if (expectedPacketNum != 0 && packetNum != expectedPacketNum) {
        return YMODEM_ERROR;
    }

    /* 接收数据 */
    for (uint16_t i = 0; i < dataSize; i++) {
        if (!ldsYmodemReceiveByte(&packet[i], YMODEM_TIMEOUT_MS)) {
            return YMODEM_TIMEOUT;
        }
    }

    /* 接收CRC */
    uint8_t crcHigh, crcLow;
    if (!ldsYmodemReceiveByte(&crcHigh, YMODEM_TIMEOUT_MS) ||
        !ldsYmodemReceiveByte(&crcLow, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }

    crcReceived = (crcHigh << 8) | crcLow;
    crcCalculated = ldsYmodemCalcCrc16(packet, dataSize);

    if (crcReceived != crcCalculated) {
        return YMODEM_CRC_ERROR;
    }

    *packetSize = dataSize;
    return YMODEM_OK;
}

/**
 * @brief 解析文件名获取版本和CRC信息
 * @param filename 文件名
 * @param fileInfo 文件信息结构体
 * @return true 解析成功, false 解析失败
 */
bool ldsYmodemParseFilename(const char* filename, lds_ymodem_file_info_t* fileInfo)
{
    const char* versionStart;
    const char* crcStart;
    size_t versionLen;
    char crcStr[9];

    /* 参数检查 */
    if (!filename || !fileInfo) {
        return false;
    }

    /* 文件名格式: AUX-EDUAudioProcessor-0001-V1.00.02-23abcdef.bin */
    if (strncmp(MODEL_ID, filename, strlen(MODEL_ID)) != 0) {
        return false;
    }

    strncpy(fileInfo->filename, filename, sizeof(fileInfo->filename) - 1);
    fileInfo->filename[sizeof(fileInfo->filename) - 1] = '\0';

    /* 查找版本号 (V后面的部分) */
    versionStart = strstr(filename, "-V");
    if (!versionStart) {
        return false;
    }
    versionStart += 2; /* 跳过 "-V" */

    /* 查找CRC32值 (最后一个-后面的8位十六进制) */
    crcStart = strrchr(filename, '-');
    if (!crcStart || strlen(crcStart) < 9) { /* "-" + 8位十六进制 */
        return false;
    }
    crcStart += 1; /* 跳过 "-" */

    /* 提取版本号 */
    versionLen = crcStart - versionStart - 1; /* -1 for the '-' before CRC */
    if (versionLen >= sizeof(fileInfo->version)) {
        versionLen = sizeof(fileInfo->version) - 1;
    }
    strncpy(fileInfo->version, versionStart, versionLen);
    fileInfo->version[versionLen] = '\0';

    /* 提取CRC32值 */
    strncpy(crcStr, crcStart, 8);
    crcStr[8] = '\0';

    /* 检查是否都是十六进制字符 */
    for (int i = 0; i < 8; i++) {
        if (!((crcStr[i] >= '0' && crcStr[i] <= '9') ||
              (crcStr[i] >= 'a' && crcStr[i] <= 'f') ||
              (crcStr[i] >= 'A' && crcStr[i] <= 'F'))) {
            return false;
        }
    }

    fileInfo->expected_crc32 = strtoul(crcStr, NULL, 16);

    return true;
}

/**
 * @brief 初始化YModem升级模块
 */
void ldsYmodemInit(void)
{
    /* YModem升级模块初始化，重置全局变量 */
    g_currentAddr = 0;
    g_receivedSize = 0;
    g_expectedPacketNum = 0;
}

/**
 * @brief 开始YModem升级流程
 * @return lds_ymodem_result_t 升级结果
 */
lds_ymodem_result_t ldsYmodemUpgrade(void)
{
    lds_ymodem_file_info_t fileInfo = {0};
    uint8_t packetNum = 0;
    uint16_t packetSize;
    lds_ymodem_result_t result;
    uint8_t retryCount = 0;
    bool firstPacket = true;
    bool fileReceived = false;

    /* 发送'C'开始传输 */
    ldsYmodemSendByte(YMODEM_C);

    while (1) {
        result = ldsYmodemReceivePacket(g_ymodemBuffer, &packetSize, g_expectedPacketNum);

        if (result == YMODEM_TIMEOUT) {
            retryCount++;
            if (retryCount >= YMODEM_MAX_RETRY) {
                /* 先发送回复，再打印错误信息 */
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("YModem timeout, max retries reached\n");
                return YMODEM_TIMEOUT;
            }
            ldsYmodemSendByte(YMODEM_C);
            continue;
        }

        if (result == YMODEM_CANCEL) {
            /* 传输被取消，直接打印错误信息 */
            printf("YModem transfer cancelled\n");
            return YMODEM_CANCEL;
        }

        if (result != YMODEM_OK) {
            ldsYmodemSendByte(YMODEM_NAK);
            retryCount++;
            if (retryCount >= YMODEM_MAX_RETRY) {
                /* 先发送回复，再打印错误信息 */
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("YModem error, max retries reached\n");
                return result;
            }
            continue;
        }

        retryCount = 0;

        /* 处理EOT */
        if (packetSize == 0) {
            ldsYmodemSendByte(YMODEM_ACK);
            if (fileReceived) {
                /* 第二次EOT，传输会话结束 */
                break;
            } else {
                /* 第一次EOT，文件传输结束，等待下一个文件或会话结束 */
                ldsYmodemSendByte(YMODEM_C);
                firstPacket = true;
                fileReceived = true;
                continue;
            }
        }

        /* 处理第一个包（文件信息包） */
        if (firstPacket) {
            firstPacket = false;

            if (g_ymodemBuffer[0] == 0) {
                /* 空文件名，传输结束 */
                ldsYmodemSendByte(YMODEM_ACK);
                break;
            }

            /* 解析文件名和大小 */
            char* filename = (char*)g_ymodemBuffer;
            char* filesizeStr = filename + strlen(filename) + 1;

            if (!ldsYmodemParseFilename(filename, &fileInfo)) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Invalid filename format %s\n", fileInfo.filename);
                return YMODEM_ERROR;
            }

            fileInfo.filesize = strtoul(filesizeStr, NULL, 10);

            if (fileInfo.filesize > APP_MAX_SIZE) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("File too large: %u bytes\n", fileInfo.filesize);
                return YMODEM_FILE_TOO_LARGE;
            }

            /* 选择写入地址（APP1区域） */
            g_currentAddr = APP1_START_ADDR;
            g_receivedSize = 0;

            /* 擦除APP1区域 */
            if (ldsFlashErase(g_currentAddr, fileInfo.filesize) != 0) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Flash erase failed\n");
                return YMODEM_ERROR;
            }

            ldsYmodemSendByte(YMODEM_ACK);
            ldsYmodemSendByte(YMODEM_C);
            packetNum = 1;
            g_expectedPacketNum = 1;
            continue;
        }

        /* 处理数据包 */
        uint32_t writeSize = packetSize;
        if (g_receivedSize + writeSize > fileInfo.filesize) {
            writeSize = fileInfo.filesize - g_receivedSize;
        }

        if (writeSize > 0) {
            if (ldsFlashWrite(g_currentAddr, g_ymodemBuffer, writeSize) != 0) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Flash write failed at 0x%08X\n", g_currentAddr);
                return YMODEM_ERROR;
            }

            g_currentAddr += writeSize;
            g_receivedSize += writeSize;
        }

        ldsYmodemSendByte(YMODEM_ACK);
        packetNum++;
        g_expectedPacketNum++;
    }

    /* 校验接收到的文件 */
    if (g_receivedSize > 0) {
        uint32_t calculatedCrc = ldsUtilCheckCrc32((const uint8_t*)APP1_START_ADDR, g_receivedSize);

        if (calculatedCrc == fileInfo.expected_crc32) {
            /* 更新BootInfo */
            BootInfo_t newBootInfo = {0};

            newBootInfo.magic = BOOT_INFO_MAGIC;
            newBootInfo.state = APP_STATE_NORMAL;
            newBootInfo.app1_crc32 = fileInfo.expected_crc32;
            newBootInfo.app1_size = g_receivedSize;
            strncpy(newBootInfo.app1_version, fileInfo.version, sizeof(newBootInfo.app1_version) - 1);
            newBootInfo.app1_version[sizeof(newBootInfo.app1_version) - 1] = '\0';

            /* 计算并更新boot info的CRC32 */
            newBootInfo.crc32 = ldsUtilCheckCrc32((const uint8_t*)&newBootInfo, (sizeof(BootInfo_t) - 4));
            printf("New boot info: target: %s, app1_crc32: 0x%08X, app1_size: %u, app1_version: %s, boot crc32: 0x%08X\n",
                   (newBootInfo.state == APP_STATE_NORMAL) ? "NORMAL" : "UPDATE",
                   newBootInfo.app1_crc32, newBootInfo.app1_size, newBootInfo.app1_version, newBootInfo.crc32);
            /* 写入新的boot info */
            if (ldsFlashErase(BOOT_INFO_ADDR, FLASH_PAGE_SIZE) == 0 &&
                ldsFlashWrite(BOOT_INFO_ADDR, (uint8_t*)&newBootInfo, sizeof(BootInfo_t)) == 0) {
                ldsFlashErase(BOOT_INFO_BK_ADDR, FLASH_PAGE_SIZE);
                ldsFlashWrite(BOOT_INFO_BK_ADDR, (uint8_t*)&newBootInfo, sizeof(BootInfo_t));
                return YMODEM_OK;
            } else {
                printf("Failed to update boot info\n");
                return YMODEM_ERROR;
            }
        } else {
            printf("File CRC32 verification failed: expected 0x%08X, got 0x%08X\n",
                   fileInfo.expected_crc32, calculatedCrc);
            return YMODEM_CRC_ERROR;
        }
    }

    return YMODEM_OK;
}